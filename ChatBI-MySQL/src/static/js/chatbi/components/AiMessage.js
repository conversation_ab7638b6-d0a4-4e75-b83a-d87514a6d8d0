import { ref, watch } from 'vue';
import { CopyIcon, CheckIcon, ThumbsDownIcon, ThumbsUpIcon, ExternalLinkIcon } from '../../utils/Icons.js';
import { renderMarkdown } from '../../utils/MarkdownRenderer.js';
import { markConversationAsBadCase, markConversationAsGoodCase } from '../services/historyService.js';

export default {
    name: 'AiMessage',
    props: {
        content: {
            type: String,
            required: true
        },
        renderedContent: {
            type: String,
            default: ''
        },
        timestamp: {
            type: String,
            default: ''
        },
        isStreaming: {
            type: Boolean,
            default: false
        },
        isError: {
            type: Boolean,
            default: false
        },
        isInterrupted: {
            type: Boolean,
            default: false
        },
        conversationId: {
            type: String,
            default: ''
        },
        isBadCase: {
            type: Boolean,
            default: false
        },
        isGoodCase: {
            type: Boolean,
            default: false
        },
        showToastNotifications: {
            type: Boolean,
            default: true
        },
        allowUnmarkBadCase: {
            type: Boolean,
            default: false
        },
        allowUnmarkGoodCase: {
            type: Boolean,
            default: true
        },
        // 外部传入的操作状态，用于禁用按钮
        isMarkingBadCase: {
            type: Boolean,
            default: false
        },
        isMarkingGoodCase: {
            type: Boolean,
            default: false
        }
    },
    emits: ['marked-as-bad-case', 'marked-as-good-case', 'share-conversation'],
    setup(props, { emit }) {
        const copied = ref(false);
        const renderedHtml = ref('');
        const isMarkedAsBadCase = ref(props.isBadCase);
        const isMarkedAsGoodCase = ref(props.isGoodCase);
        const lastRenderedContent = ref(''); // 用于跟踪上次渲染的内容
        const showToast = ref(false);
        const isMarkingBadCase = ref(false);
        const isMarkingGoodCase = ref(false);

        // 复制消息内容
        const copyContent = (text) => {
            navigator.clipboard.writeText(text)
                .then(() => {
                    copied.value = true;
                    setTimeout(() => {
                        copied.value = false;
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                });
        };

        // 分享对话
        const shareConversation = () => {
            if (!props.conversationId || props.isStreaming) return;
            emit('share-conversation', props.conversationId);
        };

        // 标记或取消标记为不良案例
        const markAsBadCase = async () => {
            if (!props.conversationId || props.isStreaming || props.isMarkingBadCase) return;

            // 根据 allowUnmarkBadCase 属性决定是否允许取消标记
            // 如果 allowUnmarkBadCase 为 false，则只能标记为 bad case，不能取消标记
            // 如果 allowUnmarkBadCase 为 true，则可以切换状态（标记或取消标记）
            const newStatus = props.allowUnmarkBadCase ? !isMarkedAsBadCase.value : true;

            // 如果当前已经是 bad case 且不允许取消标记，则直接返回
            if (isMarkedAsBadCase.value && !props.allowUnmarkBadCase) {
                return;
            }

            // 只有在启用 toast 通知时才显示
            if (props.showToastNotifications) {
                // 显示标记中的状态
                isMarkingBadCase.value = true;
                showToast.value = true;
            }

            try {
                // 调用 API 进行标记或取消标记
                await markConversationAsBadCase(props.conversationId, newStatus);

                // 更新本地状态
                isMarkedAsBadCase.value = newStatus;

                // 发送事件通知父组件
                emit('marked-as-bad-case', props.conversationId);

                // 更新为标记成功状态
                isMarkingBadCase.value = false;

                // 只有在启用 toast 通知时才处理 toast
                if (props.showToastNotifications) {
                    // 2秒后隐藏toast
                    setTimeout(() => {
                        showToast.value = false;
                    }, 2000);
                }
            } catch (error) {
                console.error('标记不良案例失败:', error);
                isMarkingBadCase.value = false;

                if (props.showToastNotifications) {
                    showToast.value = false;
                }
            }
        };

        // 标记或取消标记为Good Case
        const markAsGoodCase = async () => {
            if (!props.conversationId || props.isStreaming || props.isMarkingGoodCase) return;

            // 根据 allowUnmarkGoodCase 属性决定是否允许取消标记
            const newStatus = props.allowUnmarkGoodCase ? !isMarkedAsGoodCase.value : true;

            // 如果当前已经是 good case 且不允许取消标记，则直接返回
            if (isMarkedAsGoodCase.value && !props.allowUnmarkGoodCase) {
                return;
            }

            // 只有在启用 toast 通知时才显示
            if (props.showToastNotifications) {
                // 显示标记中的状态
                isMarkingGoodCase.value = true;
                showToast.value = true;
            }

            try {
                // 调用 API 进行标记或取消标记
                await markConversationAsGoodCase(props.conversationId, newStatus);

                // 更新本地状态
                isMarkedAsGoodCase.value = newStatus;

                // 发送事件通知父组件
                emit('marked-as-good-case', props.conversationId);

                // 更新为标记成功状态
                isMarkingGoodCase.value = false;

                // 只有在启用 toast 通知时才处理 toast
                if (props.showToastNotifications) {
                    // 2秒后隐藏toast
                    setTimeout(() => {
                        showToast.value = false;
                    }, 2000);
                }
            } catch (error) {
                console.error('标记Good Case失败:', error);
                isMarkingGoodCase.value = false;

                if (props.showToastNotifications) {
                    showToast.value = false;
                }
            }
        };

        // 监听content和renderedContent变化，更新渲染的HTML - 使用增量渲染
        watch([() => props.content, () => props.renderedContent], ([newContent, newRenderedContent], [oldContent]) => {
            // 确保内容是字符串
            const contentStr = typeof newContent === 'string' ? newContent : (newContent ? JSON.stringify(newContent) : '');
            const oldContentStr = typeof oldContent === 'string' ? oldContent : (oldContent ? JSON.stringify(oldContent) : '');

            // 如果提供了预渲染的HTML，直接使用
            if (newRenderedContent) {
                renderedHtml.value = newRenderedContent;
                lastRenderedContent.value = contentStr; // 更新最后渲染的内容
                return;
            }

            // 如果内容没有变化，不做任何事
            if (contentStr === oldContentStr) return;

            // 如果内容是之前内容的扩展（流式更新的常见情况）
            if (contentStr.startsWith(lastRenderedContent.value) && lastRenderedContent.value.length > 0) {
                // 只渲染新增的部分
                const newPart = contentStr.slice(lastRenderedContent.value.length);
                if (newPart) {
                    try {
                        // 对于简单的文本追加，可以直接渲染并追加
                        // 但为了确保HTML结构正确，对于复杂内容还是重新渲染整个内容
                        if (newPart.length < 100 && !newPart.includes('```') && !newPart.includes('|')) {
                            // 简单文本追加 - 直接渲染新部分
                            const newPartHtml = renderMarkdown(newPart);
                            // 移除新部分HTML中的包装元素，只保留内部内容
                            const contentOnly = newPartHtml.replace(/<\/?p>/g, '');

                            // 如果当前HTML为空，直接设置
                            if (!renderedHtml.value) {
                                renderedHtml.value = newPartHtml;
                            } else {
                                // 查找最后一个段落的结束标签
                                const lastParagraphIndex = renderedHtml.value.lastIndexOf('</p>');
                                if (lastParagraphIndex !== -1) {
                                    // 在最后一个段落内追加内容
                                    renderedHtml.value =
                                        renderedHtml.value.slice(0, lastParagraphIndex) +
                                        contentOnly +
                                        renderedHtml.value.slice(lastParagraphIndex);
                                } else {
                                    // 如果没有找到段落标签，直接追加
                                    renderedHtml.value += contentOnly;
                                }
                            }
                        } else {
                            // 复杂内容 - 重新渲染整个内容
                            renderedHtml.value = renderMarkdown(contentStr);
                        }
                    } catch (error) {
                        // 如果增量渲染出错，回退到完整渲染
                        console.error('增量渲染失败，回退到完整渲染:', error);
                        renderedHtml.value = renderMarkdown(contentStr);
                    }
                }
            } else {
                // 如果内容完全改变，重新渲染整个内容
                renderedHtml.value = renderMarkdown(contentStr);
            }

            // 更新最后渲染的内容
            lastRenderedContent.value = contentStr;
        }, { immediate: true });

        // Watch for isBadCase prop changes
        watch(() => props.isBadCase, (newValue) => {
            isMarkedAsBadCase.value = newValue;
        });

        // Watch for isGoodCase prop changes
        watch(() => props.isGoodCase, (newValue) => {
            isMarkedAsGoodCase.value = newValue;
        });

        return {
            copyContent,
            markAsBadCase,
            markAsGoodCase,
            shareConversation,
            renderedHtml,
            copied,
            isMarkedAsBadCase,
            isMarkedAsGoodCase,
            showToast,
            isMarkingBadCase,
            isMarkingGoodCase,
            CopyIcon,
            CheckIcon,
            ThumbsDownIcon,
            ThumbsUpIcon,
            ExternalLinkIcon
        };
    },
    template: `
        <div class="flex flex-col mb-6 group min-w-0 message ai-message"
             :class="{
                'ai-message-streaming': isStreaming,
                'ai-message-error': isError,
                'ai-message-interrupted': isInterrupted
             }">
            <div class="w-full px-4 py-3 pb-1 relative min-w-0 ai-message-content">
                <div class="markdown-content min-w-0" v-html="renderedHtml"></div>
                <div v-if="isStreaming" class="streaming-indicator">
                    <span class="loading loading-dots loading-sm"></span>
                </div>
            </div>

            <div class="flex items-center gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300 pl-4 message-footer ai-message-footer">
                <button
                    v-if="!isStreaming"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="copyContent(content)"
                    title="复制"
                >
                    <span v-if="!copied" class="message-action-icon" v-html="CopyIcon"></span>
                    <span v-else class="message-action-icon" v-html="CheckIcon"></span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="markAsGoodCase"
                    :class="{ 'good-case-marked': isMarkedAsGoodCase }"
                    :disabled="isMarkingGoodCase"
                    :title="isMarkedAsGoodCase && allowUnmarkGoodCase ? '取消标记' : '标记为Good Case'"
                >
                    <span v-if="isMarkingGoodCase" class="loading loading-spinner loading-xs"></span>
                    <span v-else class="message-action-icon" :class="{ 'good-case-icon': isMarkedAsGoodCase }" v-html="ThumbsUpIcon"></span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="markAsBadCase"
                    :class="{ 'bad-case-marked': isMarkedAsBadCase }"
                    :disabled="isMarkingBadCase"
                    :title="isMarkedAsBadCase && allowUnmarkBadCase ? '取消标记' : '标记为bad case'"
                >
                    <span v-if="isMarkingBadCase" class="loading loading-spinner loading-xs"></span>
                    <span v-else class="message-action-icon" :class="{ 'bad-case-icon': isMarkedAsBadCase }" v-html="ThumbsDownIcon"></span>
                </button>
                <button
                    v-if="!isStreaming && conversationId"
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="shareConversation"
                    title="分享对话"
                >
                    <span class="message-action-icon" v-html="ExternalLinkIcon"></span>
                </button>
                <span class="text-xs opacity-70 message-timestamp">
                    {{ isStreaming ? '正在生成...' : (isError ? '生成失败' : (isInterrupted ? '已中断' : timestamp)) }}
                </span>
            </div>

            <!-- Toast 通知 - 简约而精致的设计，符合Apple/OpenAI风格 -->
            <div v-if="showToastNotifications && showToast" class="toast toast-center toast-bottom z-50">
                <div class="alert glass-effect px-4 py-3">
                    <!-- 标记中状态 - 显示更小更精致的spinner -->
                    <span v-if="isMarkingBadCase || isMarkingGoodCase" class="loading loading-spinner loading-xs text-success mr-2"></span>
                    <!-- 标记成功状态 - 显示勾选图标 -->
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="stroke-success flex-shrink-0 h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span class="text-sm font-medium">
                        {{ isMarkingBadCase
                            ? (allowUnmarkBadCase && isMarkedAsBadCase ? '正在取消标记...' : '正在标记为不良案例...')
                            : isMarkingGoodCase
                            ? (allowUnmarkGoodCase && isMarkedAsGoodCase ? '正在取消标记...' : '正在标记为Good Case...')
                            : isMarkedAsBadCase
                            ? '已标记为不良案例'
                            : isMarkedAsGoodCase
                            ? '已标记为Good Case'
                            : '已取消标记'
                        }}
                    </span>
                </div>
            </div>
        </div>
    `
};
