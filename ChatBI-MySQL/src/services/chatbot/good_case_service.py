"""
Good case service module.

This module provides business logic for managing good cases.
"""

import functools
import time
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.good_case import (
    mark_conversation_as_good_case,
    get_conversation_good_case_status,
)

# 全局去重缓存，防止短时间内重复发送通知
_notification_cache = {}
_NOTIFICATION_COOLDOWN = 30  # 30秒内不重复发送相同通知


def feishu_notification_decorator(func):
    """
    装饰器：在标记 good case 后发送飞书通知
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原函数
        result = func(*args, **kwargs)
        
        # 如果标记成功，发送飞书通知
        if result and len(args) >= 2:
            conversation_id = args[0]
            is_good_case = args[1] if len(args) > 1 else kwargs.get('is_good_case', True)
            user_name = args[2] if len(args) > 2 else kwargs.get('user_name')
            
            try:
                # 导入飞书通知函数（延迟导入避免循环依赖）
                from src.services.feishu.message_apis import send_good_case_notification, send_good_case_unmark_notification
                
                if is_good_case:
                    # 发送标记通知
                    send_good_case_notification(conversation_id, user_name)
                else:
                    # 发送取消标记通知
                    send_good_case_unmark_notification(conversation_id, user_name)
                    
            except Exception as e:
                logger.warning(f"发送 good case 飞书通知失败: {e}")
                # 不影响主要功能，只记录警告
        
        return result
    return wrapper


@feishu_notification_decorator
def mark_good_case(conversation_id: str, is_good_case: bool = True, user_name: str = None) -> bool:
    """
    Mark or unmark a conversation as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the good case. Used for notification. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "marked" if is_good_case else "unmarked"
    logger.info(f"User {action} conversation {conversation_id} as good case, user_name: {user_name}")
    return mark_conversation_as_good_case(conversation_id, is_good_case, marked_by=user_name)


def is_good_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a good case")
    # Note: username and email parameters are kept for API compatibility but not used
    # since the good_case table design doesn't require user filtering
    return get_conversation_good_case_status(conversation_id)
